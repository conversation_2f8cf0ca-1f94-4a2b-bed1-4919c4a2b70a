/**
 * JWT 令牌管理工具
 * 提供令牌生成、验证和解析功能
 */

import jwt from 'jsonwebtoken';
import * as jose from 'jose';
import crypto from 'crypto';

export interface JWTPayload {
  userId: string;
  tenantId: string;
  email: string;
  roles?: string[];
  permissions?: string[];
  sessionId?: string;
  deviceInfo?: any;
  type?: string; // Token type (e.g., 'refresh', 'password_reset')
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export class JWTManager {
  private static readonly ACCESS_TOKEN_EXPIRES_IN = '24h'; // 访问令牌有效期
  private static readonly REFRESH_TOKEN_EXPIRES_IN = '7d'; // 刷新令牌有效期
  private static readonly PASSWORD_RESET_TOKEN_EXPIRES_IN = '1h'; // 密码重置令牌有效期
  private static readonly ISSUER = 'roasmax-local-auth';
  private static readonly AUDIENCE = 'roasmax-app';

  /**
   * 获取 JWT 密钥
   */
  private static getJWTSecret(): string {
    const secret = process.env.JWT_SECRET || process.env.APPSECRET;
    if (!secret) {
      throw new Error('JWT_SECRET 或 APPSECRET 环境变量未设置');
    }
    return secret;
  }

  /**
   * 生成访问令牌
   */
  static generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp' | 'iss' | 'aud'>): string {
    const secret = this.getJWTSecret();
    
    const tokenPayload: JWTPayload = {
      ...payload,
      iss: this.ISSUER,
      aud: this.AUDIENCE,
    };

    return jwt.sign(tokenPayload, secret, {
      expiresIn: this.ACCESS_TOKEN_EXPIRES_IN,
      algorithm: 'HS256',
    });
  }

  /**
   * 生成刷新令牌
   */
  static generateRefreshToken(userId: string, sessionId: string): string {
    const secret = this.getJWTSecret();
    
    const payload = {
      userId,
      sessionId,
      type: 'refresh',
      iss: this.ISSUER,
      aud: this.AUDIENCE,
    };

    return jwt.sign(payload, secret, {
      expiresIn: this.REFRESH_TOKEN_EXPIRES_IN,
      algorithm: 'HS256',
    });
  }

  /**
   * 生成令牌对
   */
  static generateTokenPair(payload: Omit<JWTPayload, 'iat' | 'exp' | 'iss' | 'aud'>): TokenPair {
    const sessionId = crypto.randomUUID();
    const accessToken = this.generateAccessToken({ ...payload, sessionId });
    const refreshToken = this.generateRefreshToken(payload.userId, sessionId);

    return {
      accessToken,
      refreshToken,
      expiresIn: 24 * 60 * 60, // 24小时（秒）
      tokenType: 'Bearer',
    };
  }

  /**
   * 生成密码重置令牌
   */
  static generatePasswordResetToken(userId: string): string {
    const secret = this.getJWTSecret();
    
    const payload = {
      userId,
      type: 'password_reset',
      iss: this.ISSUER,
      aud: this.AUDIENCE,
    };

    return jwt.sign(payload, secret, {
      expiresIn: this.PASSWORD_RESET_TOKEN_EXPIRES_IN,
      algorithm: 'HS256',
    });
  }

  /**
   * 验证并解析令牌（使用 jsonwebtoken）
   */
  static verifyToken(token: string): JWTPayload {
    try {
      const secret = this.getJWTSecret();
      const decoded = jwt.verify(token, secret, {
        issuer: this.ISSUER,
        audience: this.AUDIENCE,
        algorithms: ['HS256'],
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('令牌已过期');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('无效的令牌');
      } else {
        throw new Error('令牌验证失败');
      }
    }
  }

  /**
   * 验证并解析令牌（使用 jose，备用方案）
   */
  static async verifyTokenWithJose(token: string): Promise<JWTPayload> {
    try {
      const secret = new TextEncoder().encode(this.getJWTSecret());
      
      const { payload } = await jose.jwtVerify(token, secret, {
        issuer: this.ISSUER,
        audience: this.AUDIENCE,
      });

      // Validate that the payload has the required properties for our custom JWTPayload type
      const customPayload = payload as unknown as JWTPayload;

      if (!customPayload.userId) {
        throw new Error('令牌缺少用户ID');
      }

      // For access tokens, require tenantId and email
      // For refresh and password reset tokens, these fields are optional
      if (customPayload.type !== 'refresh' && customPayload.type !== 'password_reset') {
        if (!customPayload.tenantId || !customPayload.email) {
          throw new Error('令牌缺少必需的用户信息');
        }
      }

      return customPayload;
    } catch (error) {
      if (error instanceof jose.errors.JWTExpired) {
        throw new Error('令牌已过期');
      } else if (error instanceof jose.errors.JWTInvalid) {
        throw new Error('无效的令牌');
      } else {
        throw new Error('令牌验证失败');
      }
    }
  }

  /**
   * 解析令牌（不验证签名，仅用于获取信息）
   */
  static decodeToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.decode(token) as JWTPayload;
      return decoded;
    } catch (error) {
      console.error('Token decode error:', error);
      return null;
    }
  }

  /**
   * 检查令牌是否即将过期
   * @param token 令牌
   * @param thresholdMinutes 阈值（分钟），默认30分钟
   */
  static isTokenExpiringSoon(token: string, thresholdMinutes: number = 30): boolean {
    try {
      const decoded = this.decodeToken(token);
      if (!decoded || !decoded.exp) {
        return true;
      }

      const now = Math.floor(Date.now() / 1000);
      const threshold = thresholdMinutes * 60;
      
      return (decoded.exp - now) < threshold;
    } catch (error) {
      return true;
    }
  }

  /**
   * 刷新访问令牌
   */
  static async refreshAccessToken(refreshToken: string): Promise<TokenPair> {
    try {
      const decoded = this.verifyToken(refreshToken);
      
      if (decoded.type !== 'refresh') {
        throw new Error('无效的刷新令牌');
      }

      // 这里需要从数据库获取用户信息来生成新的访问令牌
      // 暂时返回基本信息，实际使用时需要查询数据库
      const payload = {
        userId: decoded.userId,
        tenantId: '', // 需要从数据库查询
        email: '', // 需要从数据库查询
        sessionId: decoded.sessionId,
      };

      return this.generateTokenPair(payload);
    } catch (error) {
      throw new Error('刷新令牌失败');
    }
  }

  /**
   * 生成会话令牌哈希（用于数据库存储）
   */
  static generateTokenHash(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * 验证密码重置令牌
   */
  static verifyPasswordResetToken(token: string): { userId: string } {
    try {
      const decoded = this.verifyToken(token);
      
      if (decoded.type !== 'password_reset') {
        throw new Error('无效的密码重置令牌');
      }

      return { userId: decoded.userId };
    } catch (error) {
      throw new Error('密码重置令牌验证失败');
    }
  }

  /**
   * 从 Authorization 头部提取令牌
   */
  static extractTokenFromHeader(authHeader: string): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    
    return authHeader.substring(7);
  }
}
