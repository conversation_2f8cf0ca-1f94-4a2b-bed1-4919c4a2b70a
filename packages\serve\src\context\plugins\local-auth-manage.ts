/**
 * 本地管理插件
 * 替换 Authing ManagementClient 插件
 */

import { LocalManagementClient } from '../../adapters/LocalManagementClient';
import { ActionContextPluginLoader } from '../../types';
import { ManagementClient } from 'authing-node-sdk';

const localAuthManagePlugin: ActionContextPluginLoader = () => {
  // 使用环境变量配置，保持与原 Authing 插件的兼容性
  const authManagement = new LocalManagementClient({
    accessKeyId: process.env.PRIVATE_ACCESSKEYID_ID,
    accessKeySecret: process.env.PRIVATE_ACCESSKEYSECRET_ID,
  });

  return {
    name: 'authingManage', // 保持原名称，确保业务代码兼容性
    plugin: authManagement,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * 本地管理客户端（兼容 Authing ManagementClient）
     */
    authingManage: ManagementClient | LocalManagementClient;
  }
}

export default localAuthManagePlugin;
