/**
 * 本地认证客户端适配器
 * 兼容 Authing AuthenticationClient 接口
 */

import { PrismaClient } from '@roasmax/database';
import { PasswordManager } from '../utils/password';
import { JWTManager, JWTPayload, TokenPair } from '../utils/jwt';
import crypto from 'crypto';

// 兼容 Authing SDK 的接口类型
export interface SignInParams {
  email: string;
  password: string;
  captchaCode?: string;
  clientIp?: string;
  customData?: any;
}

export interface SignInResult {
  statusCode: number;
  message: string;
  data: {
    access_token: string;
    id_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
    user: {
      userId: string;
      email: string;
      nickname: string;
      username: string;
      phone?: string;
      emailVerified: boolean;
      phoneVerified: boolean;
      loginsCount: number;
      lastLogin: string;
      signedUp: string;
      blocked: boolean;
      isDeleted: boolean;
      device?: string;
      browser?: string;
      company?: string;
      name?: string;
      givenName?: string;
      familyName?: string;
      middleName?: string;
      profile?: string;
      preferredUsername?: string;
      website?: string;
      gender?: string;
      birthdate?: string;
      zoneinfo?: string;
      locale?: string;
      address?: string;
      formatted?: string;
      streetAddress?: string;
      locality?: string;
      region?: string;
      postalCode?: string;
      city?: string;
      province?: string;
      country?: string;
    };
  };
}

export interface ProfileParams {
  userId?: string;
}

export interface ProfileResult {
  statusCode: number;
  message: string;
  data: any;
}

export interface UpdatePasswordParams {
  newPassword: string;
  oldPassword?: string;
  passwordEncryptType?: string;
}

export interface UpdateResult {
  statusCode: number;
  message: string;
  data: any;
}

export interface RoleListParams {
  namespace?: string;
}

export interface RoleListResult {
  statusCode: number;
  message: string;
  data: {
    totalCount: number;
    list: Array<{
      id: string;
      code: string;
      name: string;
      description: string;
      namespace: string;
      createdAt: string;
      updatedAt: string;
    }>;
  };
}

export interface AuthConfig {
  appId?: string;
  appSecret?: string;
  appHost?: string;
}

export class LocalAuthenticationClient {
  private accessToken: string = '';
  private prisma: PrismaClient;
  private config: AuthConfig;

  constructor(config: AuthConfig = {}) {
    this.config = config;
    this.prisma = new PrismaClient();
  }

  /**
   * 设置访问令牌
   */
  setAccessToken(token: string): void {
    this.accessToken = token;
  }

  /**
   * 获取当前访问令牌
   */
  getAccessToken(): string {
    return this.accessToken;
  }

  /**
   * 邮箱密码登录
   */
  async signInByEmailPassword(params: SignInParams): Promise<SignInResult> {
    const { email, password, clientIp } = params;

    try {
      // 查找用户
      const user = await this.prisma.members.findFirst({
        where: { 
          email,
          tmp_deleted_at: null 
        }
      });

      if (!user) {
        throw new Error('用户不存在或密码错误');
      }

      // 检查账户状态
      if (user.user_status !== 'Activated') {
        throw new Error('账户未激活');
      }

      // 检查账户锁定状态
      if (user.locked_until && user.locked_until > new Date()) {
        const remainingTime = Math.ceil((user.locked_until.getTime() - Date.now()) / 60000);
        throw new Error(`账户已被锁定，请 ${remainingTime} 分钟后再试`);
      }

      // 验证密码
      let passwordValid = false;
      
      if (user.password_hash && user.salt) {
        // 使用新的密码验证方式
        passwordValid = await PasswordManager.verifyPassword(password, user.password_hash, user.salt);
      } else if (user.password) {
        // 兼容旧的密码格式（如果存在）
        passwordValid = user.password === password;
      }

      if (!passwordValid) {
        await this.handleFailedLogin(user.user_id);
        throw new Error('用户不存在或密码错误');
      }

      // 检查是否需要密码重置
      if (user.password_reset_required) {
        const resetToken = JWTManager.generatePasswordResetToken(user.user_id);
        return {
          statusCode: 200,
          message: 'PASSWORD_RESET_REQUIRED',
          data: {
            access_token: resetToken,
            id_token: resetToken,
            refresh_token: '',
            token_type: 'Bearer',
            expires_in: 3600,
            user: this.formatUserData(user)
          }
        };
      }

      // 登录成功，重置失败次数
      await this.resetFailedLoginAttempts(user.user_id);

      // 生成令牌
      const tokenPayload = {
        userId: user.user_id,
        tenantId: user.tenant_id,
        email: user.email,
      };

      const tokens = JWTManager.generateTokenPair(tokenPayload);

      // 创建用户会话
      await this.createUserSession(user.user_id, user.tenant_id, tokens.accessToken, {
        ip: clientIp,
        userAgent: params.customData?.userAgent,
      });

      // 更新最后登录时间
      await this.prisma.members.update({
        where: { user_id: user.user_id },
        data: { last_login_at: new Date() }
      });

      return {
        statusCode: 200,
        message: '登录成功',
        data: {
          access_token: tokens.accessToken,
          id_token: tokens.accessToken,
          refresh_token: tokens.refreshToken,
          token_type: tokens.tokenType,
          expires_in: tokens.expiresIn,
          user: this.formatUserData(user)
        }
      };

    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * 获取用户资料
   */
  async getProfile(params: ProfileParams = {}): Promise<ProfileResult> {
    try {
      let userId = params.userId;
      
      // 如果没有提供 userId，从当前令牌中获取
      if (!userId && this.accessToken) {
        const decoded = JWTManager.verifyToken(this.accessToken);
        userId = decoded.userId;
      }

      if (!userId) {
        throw new Error('用户ID不能为空');
      }

      const user = await this.prisma.members.findFirst({
        where: { 
          user_id: userId,
          tmp_deleted_at: null 
        }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      return {
        statusCode: 200,
        message: '获取成功',
        data: this.formatUserData(user)
      };

    } catch (error) {
      console.error('Get profile error:', error);
      throw error;
    }
  }

  /**
   * 修改密码
   */
  async updatePassword(params: UpdatePasswordParams): Promise<UpdateResult> {
    try {
      if (!this.accessToken) {
        throw new Error('未登录');
      }

      const decoded = JWTManager.verifyToken(this.accessToken);
      const userId = decoded.userId;

      const user = await this.prisma.members.findFirst({
        where: { 
          user_id: userId,
          tmp_deleted_at: null 
        }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 如果提供了旧密码，需要验证
      if (params.oldPassword) {
        let oldPasswordValid = false;
        
        if (user.password_hash && user.salt) {
          oldPasswordValid = await PasswordManager.verifyPassword(params.oldPassword, user.password_hash, user.salt);
        } else if (user.password) {
          oldPasswordValid = user.password === params.oldPassword;
        }

        if (!oldPasswordValid) {
          throw new Error('原密码错误');
        }
      }

      // 生成新密码哈希
      const { hash, salt } = await PasswordManager.hashPassword(params.newPassword);

      // 更新密码
      await this.prisma.members.update({
        where: { user_id: userId },
        data: {
          password_hash: hash,
          salt: salt,
          password_reset_required: false,
          login_attempts: 0,
          locked_until: null,
        }
      });

      // 使当前用户的所有会话失效
      await this.revokeAllUserSessions(userId);

      return {
        statusCode: 200,
        message: '密码修改成功',
        data: { success: true }
      };

    } catch (error) {
      console.error('Update password error:', error);
      throw error;
    }
  }

  /**
   * 获取角色列表
   */
  async getRoleList(params: RoleListParams = {}): Promise<RoleListResult> {
    try {
      if (!this.accessToken) {
        throw new Error('未登录');
      }

      const decoded = JWTManager.verifyToken(this.accessToken);
      const tenantId = decoded.tenantId;

      const roles = await this.prisma.roles.findMany({
        where: {
          tenant_id: tenantId,
          deleted_at: null
        },
        orderBy: { created_at: 'desc' }
      });

      return {
        statusCode: 200,
        message: '获取成功',
        data: {
          totalCount: roles.length,
          list: roles.map(role => ({
            id: role.id,
            code: role.code,
            name: role.name,
            description: role.description || '',
            namespace: tenantId,
            createdAt: role.created_at.toISOString(),
            updatedAt: role.updated_at.toISOString(),
          }))
        }
      };

    } catch (error) {
      console.error('Get role list error:', error);
      throw error;
    }
  }

  /**
   * 撤销令牌
   */
  async revokeToken(token: string): Promise<boolean> {
    try {
      const tokenHash = JWTManager.generateTokenHash(token);
      
      await this.prisma.user_sessions.updateMany({
        where: { token_hash: tokenHash },
        data: { is_active: false }
      });

      return true;
    } catch (error) {
      console.error('Revoke token error:', error);
      return false;
    }
  }

  /**
   * 处理登录失败
   */
  private async handleFailedLogin(userId: string): Promise<void> {
    const user = await this.prisma.members.findFirst({
      where: { user_id: userId }
    });

    if (!user) return;

    const newAttempts = (user.login_attempts || 0) + 1;
    const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5');

    let updateData: any = { login_attempts: newAttempts };

    // 如果达到最大尝试次数，锁定账户
    if (newAttempts >= maxAttempts) {
      const lockDuration = parseInt(process.env.ACCOUNT_LOCK_DURATION || '3600'); // 默认1小时
      updateData.locked_until = new Date(Date.now() + lockDuration * 1000);
    }

    await this.prisma.members.update({
      where: { user_id: userId },
      data: updateData
    });
  }

  /**
   * 重置失败登录次数
   */
  private async resetFailedLoginAttempts(userId: string): Promise<void> {
    await this.prisma.members.update({
      where: { user_id: userId },
      data: {
        login_attempts: 0,
        locked_until: null
      }
    });
  }

  /**
   * 创建用户会话
   */
  private async createUserSession(userId: string, tenantId: string, token: string, deviceInfo: any): Promise<void> {
    const tokenHash = JWTManager.generateTokenHash(token);
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时后过期

    await this.prisma.user_sessions.create({
      data: {
        id: crypto.randomUUID(),
        user_id: userId,
        tenant_id: tenantId,
        token_hash: tokenHash,
        device_info: deviceInfo,
        ip_address: deviceInfo?.ip,
        user_agent: deviceInfo?.userAgent,
        expires_at: expiresAt,
        is_active: true,
      }
    });
  }

  /**
   * 撤销用户所有会话
   */
  private async revokeAllUserSessions(userId: string): Promise<void> {
    await this.prisma.user_sessions.updateMany({
      where: { user_id: userId },
      data: { is_active: false }
    });
  }

  /**
   * 格式化用户数据
   */
  private formatUserData(user: any): any {
    return {
      userId: user.user_id,
      email: user.email,
      nickname: user.nickname,
      username: user.account,
      phone: user.phone || undefined,
      emailVerified: user.email_verified ?? true,
      phoneVerified: false,
      loginsCount: 0,
      lastLogin: user.last_login_at?.toISOString() || user.tmp_created_at?.toISOString(),
      signedUp: user.tmp_created_at?.toISOString(),
      blocked: user.user_status !== 'Activated',
      isDeleted: !!user.tmp_deleted_at,
      company: '',
      name: user.nickname,
      givenName: '',
      familyName: '',
      middleName: '',
      profile: '',
      preferredUsername: user.account,
      website: '',
      gender: '',
      birthdate: '',
      zoneinfo: '',
      locale: '',
      address: '',
      formatted: '',
      streetAddress: '',
      locality: '',
      region: '',
      postalCode: '',
      city: '',
      province: '',
      country: '',
    };
  }
}
